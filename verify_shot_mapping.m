function verify_shot_mapping(data_filename)
%% 炮号映射验证脚本
% 功能：快速验证FDTD程序的炮号映射是否正确工作
% 输入：data_filename - 数据文件名（可选）

if nargin < 1
    % 查找最新的数据文件
    files_2d = dir('FDTD_SeismicLogging_*.mat');
    files_3d = dir('FDTD_3D_Simple_*.mat');
    
    if isempty(files_2d) && isempty(files_3d)
        fprintf('❌ 未找到FDTD数据文件\n');
        fprintf('请先运行以下程序之一：\n');
        fprintf('  - kuaisu_main.m (二维程序)\n');
        fprintf('  - kuaisu_main_3D_simple.m (三维程序)\n');
        return;
    end
    
    % 选择最新文件
    all_files = [files_2d; files_3d];
    [~, idx] = max([all_files.datenum]);
    data_filename = all_files(idx).name;
    
    fprintf('🔍 自动选择最新数据文件: %s\n', data_filename);
end

fprintf('\n=== 炮号映射验证 ===\n');

%% 1. 加载和检查数据
try
    load(data_filename);
    fprintf('✅ 数据文件加载成功\n');
catch ME
    fprintf('❌ 数据文件加载失败: %s\n', ME.message);
    return;
end

% 检查必要变量
required_vars = {'data', 'shot_start', 'shot_end', 'actual_num_shots'};
missing_vars = {};

for i = 1:length(required_vars)
    if ~exist(required_vars{i}, 'var')
        missing_vars{end+1} = required_vars{i};
    end
end

if ~isempty(missing_vars)
    fprintf('❌ 缺少必要变量: %s\n', strjoin(missing_vars, ', '));
    return;
end

fprintf('✅ 所有必要变量存在\n');

%% 2. 基本参数验证
fprintf('\n--- 基本参数检查 ---\n');

% 检查炮数范围
if shot_start < 1
    fprintf('❌ shot_start = %d < 1\n', shot_start);
    return;
else
    fprintf('✅ shot_start = %d (有效)\n', shot_start);
end

if exist('num_s', 'var') && shot_end > num_s
    fprintf('❌ shot_end = %d > num_s = %d\n', shot_end, num_s);
    return;
else
    fprintf('✅ shot_end = %d (有效)\n', shot_end);
end

if shot_start > shot_end
    fprintf('❌ shot_start (%d) > shot_end (%d)\n', shot_start, shot_end);
    return;
else
    fprintf('✅ 炮数范围: 第%d~%d炮\n', shot_start, shot_end);
end

% 检查实际炮数计算
expected_shots = shot_end - shot_start + 1;
if actual_num_shots ~= expected_shots
    fprintf('❌ actual_num_shots (%d) ≠ expected (%d)\n', actual_num_shots, expected_shots);
    return;
else
    fprintf('✅ 实际运行炮数: %d\n', actual_num_shots);
end

%% 3. 数据矩阵尺寸验证
fprintf('\n--- 数据矩阵尺寸检查 ---\n');

data_size = size(data);
fprintf('数据矩阵尺寸: %d × %d\n', data_size(1), data_size(2));

% 检查行数
if data_size(1) ~= actual_num_shots
    fprintf('❌ 数据矩阵行数 (%d) ≠ 实际炮数 (%d)\n', data_size(1), actual_num_shots);
    return;
else
    fprintf('✅ 数据矩阵行数正确\n');
end

% 检查列数（如果有N和maxt信息）
if exist('N', 'var') && exist('maxt', 'var')
    expected_cols = N * maxt;
    if data_size(2) ~= expected_cols
        fprintf('❌ 数据矩阵列数 (%d) ≠ 期望值 (%d = %d×%d)\n', ...
                data_size(2), expected_cols, N, maxt);
        return;
    else
        fprintf('✅ 数据矩阵列数正确: %d = %d道 × %d时间点\n', data_size(2), N, maxt);
    end
end

%% 4. 映射公式验证
fprintf('\n--- 映射公式验证 ---\n');

fprintf('映射公式测试:\n');
for i = 1:min(actual_num_shots, 5)  % 最多测试5炮
    matrix_row = i;
    actual_shot = matrix_row + shot_start - 1;
    calculated_row = actual_shot - shot_start + 1;
    
    if calculated_row == matrix_row
        fprintf('✅ 第%d行 ↔ 第%d炮 (公式验证通过)\n', matrix_row, actual_shot);
    else
        fprintf('❌ 映射公式错误: 行%d → 炮%d → 行%d\n', matrix_row, actual_shot, calculated_row);
        return;
    end
end

%% 5. 数据完整性检查
fprintf('\n--- 数据完整性检查 ---\n');

% 检查是否有NaN或Inf
nan_count = sum(isnan(data(:)));
inf_count = sum(isinf(data(:)));

if nan_count > 0
    fprintf('⚠️  发现 %d 个 NaN 值\n', nan_count);
else
    fprintf('✅ 无 NaN 值\n');
end

if inf_count > 0
    fprintf('⚠️  发现 %d 个 Inf 值\n', inf_count);
else
    fprintf('✅ 无 Inf 值\n');
end

% 检查数据范围
data_min = min(data(:));
data_max = max(data(:));
data_range = data_max - data_min;

fprintf('数据值范围: [%.2e, %.2e]\n', data_min, data_max);

if data_range == 0
    fprintf('⚠️  所有数据值相同，可能计算有问题\n');
elseif abs(data_max) > 1e10 || abs(data_min) > 1e10
    fprintf('⚠️  数据值过大，可能存在数值不稳定\n');
else
    fprintf('✅ 数据值范围正常\n');
end

%% 6. 边界情况测试
fprintf('\n--- 边界情况测试 ---\n');

% 测试第一炮和最后一炮的访问
try
    first_shot_data = data(1, :);
    fprintf('✅ 第一炮数据访问成功 (对应第%d炮)\n', shot_start);
catch ME
    fprintf('❌ 第一炮数据访问失败: %s\n', ME.message);
end

try
    last_shot_data = data(actual_num_shots, :);
    fprintf('✅ 最后一炮数据访问成功 (对应第%d炮)\n', shot_end);
catch ME
    fprintf('❌ 最后一炮数据访问失败: %s\n', ME.message);
end

%% 7. 辅助函数测试
fprintf('\n--- 辅助函数测试 ---\n');

if exist('get_shot_data.m', 'file')
    try
        % 测试提取中间炮的数据
        if actual_num_shots >= 2
            mid_shot = shot_start + floor(actual_num_shots/2);
            if exist('maxt', 'var')
                extracted_data = get_shot_data(data, shot_start, maxt, mid_shot);
                fprintf('✅ get_shot_data 函数工作正常\n');
            else
                fprintf('⚠️  缺少 maxt 参数，无法测试 get_shot_data\n');
            end
        else
            fprintf('⚠️  炮数太少，无法测试 get_shot_data\n');
        end
    catch ME
        fprintf('❌ get_shot_data 函数测试失败: %s\n', ME.message);
    end
else
    fprintf('⚠️  get_shot_data.m 不存在，建议运行 shot_mapping_helper 创建\n');
end

%% 8. 生成验证报告
fprintf('\n=== 验证报告 ===\n');

fprintf('文件信息:\n');
fprintf('  文件名: %s\n', data_filename);
fprintf('  文件大小: %.2f MB\n', dir(data_filename).bytes/1024/1024);

fprintf('\n炮数信息:\n');
if exist('num_s', 'var')
    fprintf('  总炮数: %d\n', num_s);
end
fprintf('  运行范围: 第%d~%d炮\n', shot_start, shot_end);
fprintf('  实际运行: %d 炮\n', actual_num_shots);

fprintf('\n数据信息:\n');
fprintf('  矩阵尺寸: %d × %d\n', data_size(1), data_size(2));
if exist('N', 'var') && exist('maxt', 'var')
    fprintf('  检波器数: %d\n', N);
    fprintf('  时间采样: %d\n', maxt);
end
fprintf('  数据范围: [%.2e, %.2e]\n', data_min, data_max);

fprintf('\n映射关系:\n');
fprintf('  data(1,:) → 第%d炮\n', shot_start);
if actual_num_shots > 1
    fprintf('  data(%d,:) → 第%d炮\n', actual_num_shots, shot_end);
end

fprintf('\n✅ 炮号映射验证完成！\n');
fprintf('如需查看详细映射信息，请运行: shot_mapping_helper(''%s'')\n', data_filename);

end
