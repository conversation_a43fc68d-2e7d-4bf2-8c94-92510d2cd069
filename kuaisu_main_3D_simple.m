clc;clear;
%==========================================================================
%                    简化版三维声波测井FDTD正演模拟程序
%==========================================================================
% 功能说明：基于二维程序扩展的三维弹性波FDTD算法（简化版）
% 特点：去除复杂PML边界，专注核心三维FDTD算法实现
% 适用于：三维声波测井正演模拟，包括井孔、围岩、侵入带
%==========================================================================

fprintf('=== 简化版三维声波测井FDTD程序 ===\n');

%--------------------------------------------------------------------------
%                    1. 基本参数设置
%--------------------------------------------------------------------------

% 介质物理参数
po1=1.0*10^3; vp1=1500; vs1=0;      % 井孔介质（泥浆）
po2=2.3*10^3; vp2=4500; vs2=2300;   % 地层介质（围岩）
f0=10*10^3;                         % 震源主频率 [Hz]
cal=0.1;                            % 井径 [m]

% 网格参数
la1=vp1/f0;                         % 波长
vmax=4500;                          % 最大速度
n1=9/8; n2=-1/24;                   % 四阶差分系数
dx=la1/10; dy=dx; dz=dx;            % 空间步长（三个方向相同）
dt=dx/(1.7321*vmax*(abs(n1)+abs(n2))); % 时间步长

% 计算域尺寸（简化版，较小尺寸）
pml=20;                             % 简化边界厚度
nx=2*pml+100;                       % X方向网格数
ny=nx;                              % Y方向网格数（与X相同）
nz=2*pml+600;                       % Z方向网格数

% 测井仪器参数
num_s=67;                           % 总炮数
L_StoR=1.5; L_RtoR=0.15;           % 源距和接收器间距
len_StoR=fix(L_StoR/dz); len_RtoR=fix(L_RtoR/dz);

%--------------------------------------------------------------------------
%                    1.5 炮数范围控制设置
%--------------------------------------------------------------------------
% 功能说明：用户可以设定运行特定的炮数范围，用于调试或部分计算
% 使用方法：
%   - shot_start = 1, shot_end = num_s：运行全部炮数
%   - shot_start = 10, shot_end = 10：只运行第10炮
%   - shot_start = 20, shot_end = 30：运行第20-30炮

shot_start = 1;      % 起始炮号（1 <= shot_start <= num_s）
shot_end = 3;        % 结束炮号（shot_start <= shot_end <= num_s）

% 参数检查和修正
if shot_start < 1
    shot_start = 1;
    fprintf('警告：起始炮号小于1，已修正为1\n');
end
if shot_end > num_s
    shot_end = num_s;
    fprintf('警告：结束炮号大于总炮数，已修正为%d\n', num_s);
end
if shot_start > shot_end
    temp = shot_start;
    shot_start = shot_end;
    shot_end = temp;
    fprintf('警告：起始炮号大于结束炮号，已自动交换\n');
end

% 实际运行的炮数
actual_num_shots = shot_end - shot_start + 1;

fprintf('=== 三维FDTD程序设置信息 ===\n');
fprintf('网格尺寸: %d×%d×%d (%.1f×%.1f×%.1f m)\n', nx,ny,nz, nx*dx,ny*dy,nz*dz);
fprintf('总炮数：%d\n', num_s);
fprintf('运行范围：第%d炮 ~ 第%d炮\n', shot_start, shot_end);
fprintf('实际运行炮数：%d\n', actual_num_shots);
fprintf('预计内存使用: %.2f GB\n', memory_gb);
fprintf('==============================\n');

%--------------------------------------------------------------------------
%                    2. 内存分配
%--------------------------------------------------------------------------

fprintf('分配三维场变量内存...\n');

% 速度场（简化：只保留主场，不含PML辅助场）
Vx=zeros(nz,ny,nx); Vy=zeros(nz,ny,nx); Vz=zeros(nz,ny,nx);

% 应力场（简化：只保留主场）
Txx=zeros(nz,ny,nx); Tyy=zeros(nz,ny,nx); Tzz=zeros(nz,ny,nx);
Txy=zeros(nz,ny,nx); Txz=zeros(nz,ny,nx); Tyz=zeros(nz,ny,nx);

% 计算内存使用
memory_gb = 9 * nx * ny * nz * 8 / 1024^3;
fprintf('内存使用: %.2f GB\n', memory_gb);

%--------------------------------------------------------------------------
%                    3. 三维地质模型构建
%--------------------------------------------------------------------------

fprintf('构建三维地质模型...\n');

% 材料参数矩阵
vp=zeros(nz,ny,nx); vs=zeros(nz,ny,nx); dens=zeros(nz,ny,nx);

% 井孔位置
med_x=fix(nx/2); med_y=fix(ny/2); l_cal=ceil(cal/dx);

% 侵入带参数
Formation_D=1.0; Formation_H=1.0;
Formation_DIter=ceil(Formation_D/dx); Formation_HIter=ceil(Formation_H/dz);
Vpout=2300; Vsout=1300; Denout=1800;

% 构建三维模型
for k=1:nz
    for j=1:ny
        for i=1:nx
            r_distance = sqrt((i-med_x)^2 + (j-med_y)^2);
            
            if r_distance <= l_cal
                % 井孔区域
                vp(k,j,i)=vp1; vs(k,j,i)=vs1; dens(k,j,i)=po1;
            else
                % 围岩区域
                vp(k,j,i)=vp2; vs(k,j,i)=vs2; dens(k,j,i)=po2;
            end
        end
    end
    if mod(k,100)==0, fprintf('  模型构建进度: %d/%d\n', k, nz); end
end

% 添加侵入带
z_start = nz/2-ceil(Formation_HIter/2); z_end = nz/2+ceil(Formation_HIter/2);
for k=z_start:z_end
    for j=1:ny
        for i=1:nx
            r_distance = sqrt((i-med_x)^2 + (j-med_y)^2);
            if r_distance > l_cal && r_distance <= l_cal+Formation_DIter
                vp(k,j,i)=Vpout; vs(k,j,i)=Vsout; dens(k,j,i)=Denout;
            end
        end
    end
end

fprintf('三维地质模型构建完成\n');

%--------------------------------------------------------------------------
%                    4. 弹性参数计算
%--------------------------------------------------------------------------

fprintf('计算弹性参数...\n');

% Lamé参数
miu = dens .* vs.^2;
lmd = dens .* vp.^2 - 2*miu;

% FDTD系数（简化计算）
p0 = dt ./ dens;                    % 速度更新系数
p1 = dt .* (lmd + 2*miu);          % 正应力系数
p2 = dt .* lmd;                     % 正应力交叉项系数
p3 = dt .* miu;                     % 切应力系数

%--------------------------------------------------------------------------
%                    5. 震源函数
%--------------------------------------------------------------------------

% Ricker子波
T=1.5/f0; maxt=fix(T/dt)+1;
f=zeros(maxt,1);
for t=1:maxt
    T_current = (t-1)*dt;
    if T_current <= T
        temp = pi*f0*(T_current - T/2);
        f(t) = (1 - 2*temp^2) * exp(-temp^2);
    end
end

fprintf('Ricker子波: 主频%d Hz, 时间步数%d\n', f0, maxt);

%--------------------------------------------------------------------------
%                    6. 数据存储初始化
%--------------------------------------------------------------------------

N=21;  % 检波器数量
data=zeros(actual_num_shots, N*maxt);
X=zeros(N,maxt);

fprintf('数据矩阵: %d炮 × %d道 × %d时间点\n', actual_num_shots, N, maxt);

%==========================================================================
%                    7. 三维FDTD主循环
%==========================================================================

fprintf('\n=== 开始三维FDTD计算 ===\n');
tic;

% 多炮循环
for count_s = shot_start:shot_end
    pos_s = nz-3*pml-(count_s-1)*len_RtoR;  % 炮点Z位置
    fprintf('计算第%d炮 (Z=%d)...\n', count_s, pos_s);

    % 场变量归零
    Vx(:,:,:)=0; Vy(:,:,:)=0; Vz(:,:,:)=0;
    Txx(:,:,:)=0; Tyy(:,:,:)=0; Tzz(:,:,:)=0;
    Txy(:,:,:)=0; Txz(:,:,:)=0; Tyz(:,:,:)=0;

    % 时间步进循环
    for count_t = 1:maxt

        %==============================================================
        %                    应力场更新
        %==============================================================

        % 震源加载（在井孔中心）
        Txx(pos_s,med_y,med_x) = Txx(pos_s,med_y,med_x) + f(count_t);
        Tyy(pos_s,med_y,med_x) = Tyy(pos_s,med_y,med_x) + f(count_t);
        Tzz(pos_s,med_y,med_x) = Tzz(pos_s,med_y,med_x) + f(count_t);

        % 内部区域应力更新（避开边界）
        for k = 3:nz-2
            for j = 3:ny-2
                for i = 3:nx-2
                    % 速度梯度计算（四阶精度）
                    dvx_dx = (n1*(Vx(k,j,i+1)-Vx(k,j,i-1)) + n2*(Vx(k,j,i+2)-Vx(k,j,i-2)))/dx;
                    dvy_dy = (n1*(Vy(k,j+1,i)-Vy(k,j-1,i)) + n2*(Vy(k,j+2,i)-Vy(k,j-2,i)))/dy;
                    dvz_dz = (n1*(Vz(k+1,j,i)-Vz(k-1,j,i)) + n2*(Vz(k+2,j,i)-Vz(k-2,j,i)))/dz;

                    % 正应力更新
                    Txx(k,j,i) = Txx(k,j,i) + p1(k,j,i)*dvx_dx + p2(k,j,i)*(dvy_dy + dvz_dz);
                    Tyy(k,j,i) = Tyy(k,j,i) + p2(k,j,i)*dvx_dx + p1(k,j,i)*dvy_dy + p2(k,j,i)*dvz_dz;
                    Tzz(k,j,i) = Tzz(k,j,i) + p2(k,j,i)*dvx_dx + p2(k,j,i)*dvy_dy + p1(k,j,i)*dvz_dz;

                    % 切应力更新
                    dvx_dy = (n1*(Vx(k,j+1,i)-Vx(k,j-1,i)) + n2*(Vx(k,j+2,i)-Vx(k,j-2,i)))/dy;
                    dvy_dx = (n1*(Vy(k,j,i+1)-Vy(k,j,i-1)) + n2*(Vy(k,j,i+2)-Vy(k,j,i-2)))/dx;
                    dvx_dz = (n1*(Vx(k+1,j,i)-Vx(k-1,j,i)) + n2*(Vx(k+2,j,i)-Vx(k-2,j,i)))/dz;
                    dvz_dx = (n1*(Vz(k,j,i+1)-Vz(k,j,i-1)) + n2*(Vz(k,j,i+2)-Vz(k,j,i-2)))/dx;
                    dvy_dz = (n1*(Vy(k+1,j,i)-Vy(k-1,j,i)) + n2*(Vy(k+2,j,i)-Vy(k-2,j,i)))/dz;
                    dvz_dy = (n1*(Vz(k,j+1,i)-Vz(k,j-1,i)) + n2*(Vz(k,j+2,i)-Vz(k,j-2,i)))/dy;

                    Txy(k,j,i) = Txy(k,j,i) + p3(k,j,i)*(dvx_dy + dvy_dx);
                    Txz(k,j,i) = Txz(k,j,i) + p3(k,j,i)*(dvx_dz + dvz_dx);
                    Tyz(k,j,i) = Tyz(k,j,i) + p3(k,j,i)*(dvy_dz + dvz_dy);
                end
            end
        end

        %==============================================================
        %                    速度场更新
        %==============================================================

        % 内部区域速度更新
        for k = 3:nz-2
            for j = 3:ny-2
                for i = 3:nx-2
                    % 应力梯度计算
                    dtxx_dx = (n1*(Txx(k,j,i+1)-Txx(k,j,i-1)) + n2*(Txx(k,j,i+2)-Txx(k,j,i-2)))/dx;
                    dtxy_dy = (n1*(Txy(k,j+1,i)-Txy(k,j-1,i)) + n2*(Txy(k,j+2,i)-Txy(k,j-2,i)))/dy;
                    dtxz_dz = (n1*(Txz(k+1,j,i)-Txz(k-1,j,i)) + n2*(Txz(k+2,j,i)-Txz(k-2,j,i)))/dz;

                    dtxy_dx = (n1*(Txy(k,j,i+1)-Txy(k,j,i-1)) + n2*(Txy(k,j,i+2)-Txy(k,j,i-2)))/dx;
                    dtyy_dy = (n1*(Tyy(k,j+1,i)-Tyy(k,j-1,i)) + n2*(Tyy(k,j+2,i)-Tyy(k,j-2,i)))/dy;
                    dtyz_dz = (n1*(Tyz(k+1,j,i)-Tyz(k-1,j,i)) + n2*(Tyz(k+2,j,i)-Tyz(k-2,j,i)))/dz;

                    dtxz_dx = (n1*(Txz(k,j,i+1)-Txz(k,j,i-1)) + n2*(Txz(k,j,i+2)-Txz(k,j,i-2)))/dx;
                    dtyz_dy = (n1*(Tyz(k,j+1,i)-Tyz(k,j-1,i)) + n2*(Tyz(k,j+2,i)-Tyz(k,j-2,i)))/dy;
                    dtzz_dz = (n1*(Tzz(k+1,j,i)-Tzz(k-1,j,i)) + n2*(Tzz(k+2,j,i)-Tzz(k-2,j,i)))/dz;

                    % 速度更新
                    Vx(k,j,i) = Vx(k,j,i) + p0(k,j,i)*(dtxx_dx + dtxy_dy + dtxz_dz);
                    Vy(k,j,i) = Vy(k,j,i) + p0(k,j,i)*(dtxy_dx + dtyy_dy + dtyz_dz);
                    Vz(k,j,i) = Vz(k,j,i) + p0(k,j,i)*(dtxz_dx + dtyz_dy + dtzz_dz);
                end
            end
        end

        % 进度显示
        if mod(count_t,50)==0
            fprintf('  时间步: %d/%d\n', count_t, maxt);
        end

        %==============================================================
        %                    数据采集
        %==============================================================

        % 检波器数据采集
        onep = zeros(N,1);
        cc = 1;
        for rec_pos = pos_s-len_StoR:-len_RtoR:pos_s-len_StoR-(N-1)*len_RtoR
            onep(cc) = Txx(rec_pos, med_y, med_x);  % 采集X方向正应力
            cc = cc + 1;
        end
        X(:,count_t) = onep;

    end  % 时间步循环结束

    % 存储单炮数据
    data_index = count_s - shot_start + 1;
    for i = 1:N
        data(data_index, (i-1)*maxt+1:i*maxt) = X(i,:);
    end

    fprintf('  第%d炮完成\n', count_s);

end  % 炮点循环结束

execution_time = toc;
fprintf('\n=== 三维FDTD计算完成 ===\n');
fprintf('总执行时间: %.2f 秒 (%.2f 分钟)\n', execution_time, execution_time/60);

%==========================================================================
%                    8. 数据保存与结果输出
%==========================================================================

% 生成文件名
current_time = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));
data_filename = ['FDTD_3D_Simple_', current_time, '.mat'];

% 保存数据
fprintf('保存数据到: %s\n', data_filename);
save(data_filename, ...
    'data', 'X', ...                      % 主要结果数据
    'vp', 'vs', 'dens', 'lmd', 'miu', ... % 地层参数
    'p0', 'p1', 'p2', 'p3', ...           % FDTD系数
    'f', ...                              % 震源函数
    'dx', 'dy', 'dz', 'dt', ...           % 网格参数
    'nx', 'ny', 'nz', 'maxt', ...         % 网格尺寸
    'num_s', 'N', ...                     % 炮数和检波器数
    'shot_start', 'shot_end', 'actual_num_shots', ... % 炮数控制
    'len_StoR', 'len_RtoR', ...           % 仪器参数
    'med_x', 'med_y', ...                 % 井孔位置
    'f0', 'cal', ...                      % 震源和井径
    'po1', 'vp1', 'vs1', 'po2', 'vp2', 'vs2', ... % 介质参数
    'execution_time', 'current_time');    % 时间信息

% 显示结果信息
fprintf('\n=== 结果信息 ===\n');
fprintf('文件名: %s\n', data_filename);
fprintf('文件大小: %.2f MB\n', dir(data_filename).bytes/1024/1024);
fprintf('网格尺寸: %d×%d×%d\n', nx, ny, nz);
fprintf('运行炮数: %d (第%d~%d炮)\n', actual_num_shots, shot_start, shot_end);
fprintf('检波器数: %d, 时间步数: %d\n', N, maxt);
fprintf('数据矩阵: %d×%d\n', size(data,1), size(data,2));
fprintf('平均每炮时间: %.2f 秒\n', execution_time/actual_num_shots);

% 使用示例
fprintf('\n=== 数据使用示例 ===\n');
fprintf('load(''%s'');  %% 加载数据\n', data_filename);
fprintf('imagesc(data);  %% 显示所有炮数据\n');
fprintf('plot(data(1,:));  %% 显示第1个运行炮的数据\n');
fprintf('注意：data矩阵第1行对应第%d炮，第%d行对应第%d炮\n', shot_start, actual_num_shots, shot_end);
fprintf('figure; imagesc(squeeze(vp(:,:,med_x)));  %% 显示XZ剖面速度模型\n');
fprintf('figure; imagesc(squeeze(vp(nz/2,:,:)));   %% 显示XY剖面速度模型\n');

fprintf('\n=== 三维程序运行完成 ===\n');
