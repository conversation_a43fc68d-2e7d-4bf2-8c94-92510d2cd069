% 测试脚本：对比Ricker子波和Cosine震源
% 功能：验证新的cosine震源函数是否正确实现

clc; clear; close all;

%% 参数设置
f0 = 10*10^3;           % 主频率 [Hz]
dt = 1e-6;              % 时间步长 [s]
maxt = 2000;            % 时间采样点数
t = (0:maxt-1).*dt;     % 时间轴

%% 1. 原始Ricker子波
kexi = f0^2/0.1512;     % <PERSON>er子波形状参数
ts_ricker = 1.5/f0;     % Ricker子波时移参数
f_ricker = zeros(1,maxt);

for count_i = 1:1:maxt
    T = t(count_i) - ts_ricker;
    f_ricker(count_i) = 2*kexi*(1-2*kexi*T^2)*exp(-kexi*T^2);
end

%% 2. 新的Cosine震源
Ts = 3/f0;              % 震源持续时间（3个周期）
ts_cosine = Ts/2;       % 震源中心时间
f_cosine = zeros(1,maxt);

for count_i = 1:1:maxt
    T = t(count_i);
    if T >= 0 && T <= Ts
        % Cosine震源公式
        envelope = 0.5 * (1 - cos(2*pi/Ts*(T - Ts/2)));
        carrier = cos(2*pi*f0*(T - Ts/2));
        f_cosine(count_i) = envelope * carrier;
    else
        f_cosine(count_i) = 0;
    end
end

%% 3. 绘制对比图
figure('Position', [100, 100, 1200, 800]);

% 子图1：时域波形对比
subplot(2,2,1);
plot(t*1e6, f_ricker, 'b-', 'LineWidth', 1.5); hold on;
plot(t*1e6, f_cosine, 'r-', 'LineWidth', 1.5);
xlabel('时间 (μs)');
ylabel('振幅');
title('时域波形对比');
legend('Ricker子波', 'Cosine震源', 'Location', 'best');
grid on;
xlim([0, 500]);  % 显示前500微秒

% 子图2：Ricker子波详细
subplot(2,2,2);
plot(t*1e6, f_ricker, 'b-', 'LineWidth', 2);
xlabel('时间 (μs)');
ylabel('振幅');
title('Ricker子波');
grid on;
xlim([0, 500]);

% 子图3：Cosine震源详细
subplot(2,2,3);
plot(t*1e6, f_cosine, 'r-', 'LineWidth', 2);
xlabel('时间 (μs)');
ylabel('振幅');
title('Cosine震源');
grid on;
xlim([0, 500]);

% 子图4：频谱对比
subplot(2,2,4);
% 计算频谱
N = length(f_ricker);
freq = (0:N-1)/(N*dt)/1000;  % 频率轴 (kHz)

F_ricker = abs(fft(f_ricker));
F_cosine = abs(fft(f_cosine));

plot(freq, F_ricker, 'b-', 'LineWidth', 1.5); hold on;
plot(freq, F_cosine, 'r-', 'LineWidth', 1.5);
xlabel('频率 (kHz)');
ylabel('幅度');
title('频谱对比');
legend('Ricker子波', 'Cosine震源', 'Location', 'best');
grid on;
xlim([0, 50]);  % 显示0-50kHz

%% 4. 输出统计信息
fprintf('=== 震源函数对比分析 ===\n');
fprintf('主频率: %.1f kHz\n', f0/1000);
fprintf('时间步长: %.2f μs\n', dt*1e6);
fprintf('总时长: %.1f ms\n', maxt*dt*1000);

fprintf('\nRicker子波:\n');
fprintf('  最大振幅: %.4f\n', max(abs(f_ricker)));
fprintf('  有效持续时间: %.1f μs\n', ts_ricker*2*1e6);

fprintf('\nCosine震源:\n');
fprintf('  最大振幅: %.4f\n', max(abs(f_cosine)));
fprintf('  设计持续时间: %.1f μs\n', Ts*1e6);
fprintf('  周期数: %.1f\n', Ts*f0);

fprintf('\n=== 测试完成 ===\n');
