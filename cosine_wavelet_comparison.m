clc; clear; close all;

%% 参数设置
f0 = 10e3;          % 主频率 10kHz
dt = 1e-6;          % 时间步长 1μs
Ts = 3/f0;          % 震源持续时间（3个周期）
maxt = round(5*Ts/dt); % 总时间点数（5倍震源持续时间）
t = (0:maxt-1) * dt;   % 时间轴

%% 你的震源函数（时间偏移版本）
f_your = zeros(1, maxt);
for i = 1:maxt
    T = t(i);
    if T >= 0 && T <= Ts
        envelope = 0.5 * (1 - cos(2*pi/Ts*(T - Ts/2)));
        carrier = cos(2*pi*f0*(T - Ts/2));
        f_your(i) = envelope * carrier;
    end
end

%% 标准cosine子波（Hanning窗调制）
f_standard = zeros(1, maxt);
for i = 1:maxt
    T = t(i);
    if T >= 0 && T <= Ts
        envelope = 0.5 * (1 - cos(2*pi*T/Ts));
        carrier = cos(2*pi*f0*T);
        f_standard(i) = envelope * carrier;
    end
end

%% Ricker子波（对比参考）
f_ricker = zeros(1, maxt);
for i = 1:maxt
    T = t(i) - Ts/2;  % Ricker子波通常以中心时间为参考
    a = pi * f0 * T;
    f_ricker(i) = (1 - 2*a^2) * exp(-a^2);
end

%% 频谱分析
N = 2^nextpow2(maxt);
freq = (0:N/2-1) * (1/dt) / N;

% 计算频谱
F_your = fft(f_your, N);
F_standard = fft(f_standard, N);
F_ricker = fft(f_ricker, N);

% 取幅度谱
amp_your = abs(F_your(1:N/2));
amp_standard = abs(F_standard(1:N/2));
amp_ricker = abs(F_ricker(1:N/2));

%% 绘图对比
figure('Position', [100, 100, 1200, 800]);

% 时域波形对比
subplot(2,3,1);
plot(t*1e6, f_your, 'r-', 'LineWidth', 2); hold on;
plot(t*1e6, f_standard, 'b--', 'LineWidth', 2);
plot(t*1e6, f_ricker, 'g:', 'LineWidth', 2);
xlabel('时间 (μs)'); ylabel('振幅');
title('时域波形对比');
legend('你的震源', '标准cosine', 'Ricker子波', 'Location', 'best');
grid on;

% 包络函数对比
subplot(2,3,2);
t_env = t(t <= Ts);
env_your = 0.5 * (1 - cos(2*pi/Ts*(t_env - Ts/2)));
env_standard = 0.5 * (1 - cos(2*pi*t_env/Ts));
plot(t_env*1e6, env_your, 'r-', 'LineWidth', 2); hold on;
plot(t_env*1e6, env_standard, 'b--', 'LineWidth', 2);
xlabel('时间 (μs)'); ylabel('包络振幅');
title('包络函数对比');
legend('你的包络', '标准包络', 'Location', 'best');
grid on;

% 载波函数对比
subplot(2,3,3);
carrier_your = cos(2*pi*f0*(t_env - Ts/2));
carrier_standard = cos(2*pi*f0*t_env);
plot(t_env*1e6, carrier_your, 'r-', 'LineWidth', 1); hold on;
plot(t_env*1e6, carrier_standard, 'b--', 'LineWidth', 1);
xlabel('时间 (μs)'); ylabel('载波振幅');
title('载波函数对比');
legend('你的载波', '标准载波', 'Location', 'best');
grid on;

% 频谱对比
subplot(2,3,4);
plot(freq/1e3, amp_your/max(amp_your), 'r-', 'LineWidth', 2); hold on;
plot(freq/1e3, amp_standard/max(amp_standard), 'b--', 'LineWidth', 2);
plot(freq/1e3, amp_ricker/max(amp_ricker), 'g:', 'LineWidth', 2);
xlabel('频率 (kHz)'); ylabel('归一化幅度');
title('频谱对比');
legend('你的震源', '标准cosine', 'Ricker子波', 'Location', 'best');
grid on; xlim([0, 30]);

% 相位谱对比
subplot(2,3,5);
phase_your = angle(F_your(1:N/2));
phase_standard = angle(F_standard(1:N/2));
plot(freq/1e3, unwrap(phase_your), 'r-', 'LineWidth', 2); hold on;
plot(freq/1e3, unwrap(phase_standard), 'b--', 'LineWidth', 2);
xlabel('频率 (kHz)'); ylabel('相位 (rad)');
title('相位谱对比');
legend('你的震源', '标准cosine', 'Location', 'best');
grid on; xlim([0, 30]);

% 能量分布
subplot(2,3,6);
energy_your = cumsum(f_your.^2) / sum(f_your.^2);
energy_standard = cumsum(f_standard.^2) / sum(f_standard.^2);
plot(t*1e6, energy_your, 'r-', 'LineWidth', 2); hold on;
plot(t*1e6, energy_standard, 'b--', 'LineWidth', 2);
xlabel('时间 (μs)'); ylabel('累积能量比例');
title('能量分布对比');
legend('你的震源', '标准cosine', 'Location', 'best');
grid on;

%% 数值特性分析
fprintf('=== 震源函数特性对比 ===\n');
fprintf('参数设置：f0 = %.1f kHz, Ts = %.2f μs\n', f0/1e3, Ts*1e6);
fprintf('\n1. 时域特性：\n');
fprintf('   你的震源 - 最大值: %.4f, 最小值: %.4f\n', max(f_your), min(f_your));
fprintf('   标准cosine - 最大值: %.4f, 最小值: %.4f\n', max(f_standard), min(f_standard));

fprintf('\n2. 起始和结束值：\n');
fprintf('   你的震源 - 起始: %.6f, 结束: %.6f\n', f_your(1), f_your(find(t<=Ts, 1, 'last')));
fprintf('   标准cosine - 起始: %.6f, 结束: %.6f\n', f_standard(1), f_standard(find(t<=Ts, 1, 'last')));

fprintf('\n3. 主频分析：\n');
[~, idx_your] = max(amp_your);
[~, idx_standard] = max(amp_standard);
fprintf('   你的震源主频: %.2f kHz\n', freq(idx_your)/1e3);
fprintf('   标准cosine主频: %.2f kHz\n', freq(idx_standard)/1e3);

fprintf('\n4. 能量集中度（90%%能量时间窗）：\n');
t90_your = t(find(energy_your >= 0.9, 1)) * 1e6;
t90_standard = t(find(energy_standard >= 0.9, 1)) * 1e6;
fprintf('   你的震源: %.2f μs\n', t90_your);
fprintf('   标准cosine: %.2f μs\n', t90_standard);
