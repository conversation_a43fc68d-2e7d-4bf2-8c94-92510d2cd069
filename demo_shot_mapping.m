%% 炮号映射功能演示脚本
% 功能：演示如何正确理解和使用FDTD程序的炮号映射机制

clc; clear; close all;

fprintf('=== FDTD程序炮号映射功能演示 ===\n\n');

%% 1. 问题说明
fprintf('1. 问题背景:\n');
fprintf('   用户发现程序虽然可以选择任意炮数范围运行，\n');
fprintf('   但保存的数据矩阵总是从第1行开始，容易造成混淆。\n\n');

fprintf('   例如：运行第10~15炮，数据矩阵仍然是6×N的矩阵，\n');
fprintf('   第1行对应第10炮，第6行对应第15炮。\n\n');

%% 2. 解决方案说明
fprintf('2. 解决方案:\n');
fprintf('   程序已经正确保存了炮号映射信息：\n');
fprintf('   - shot_start: 起始炮号\n');
fprintf('   - shot_end: 结束炮号  \n');
fprintf('   - actual_num_shots: 实际运行炮数\n');
fprintf('   - data矩阵: [actual_num_shots × (N*maxt)]\n\n');

%% 3. 模拟不同炮数范围的情况
fprintf('3. 不同炮数范围示例:\n\n');

scenarios = {
    [1, 3, '运行前3炮（默认）'];
    [10, 12, '运行第10~12炮'];
    [25, 25, '只运行第25炮'];
    [50, 55, '运行第50~55炮']
};

for i = 1:size(scenarios, 1)
    shot_start = scenarios{i, 1};
    shot_end = scenarios{i, 2};
    description = scenarios{i, 3};
    actual_num_shots = shot_end - shot_start + 1;
    
    fprintf('场景%d: %s\n', i, description);
    fprintf('  设置: shot_start=%d, shot_end=%d\n', shot_start, shot_end);
    fprintf('  结果: data矩阵尺寸 %d×N*maxt\n', actual_num_shots);
    fprintf('  映射: data(1,:)对应第%d炮, data(%d,:)对应第%d炮\n', ...
            shot_start, actual_num_shots, shot_end);
    fprintf('\n');
end

%% 4. 数据访问方法演示
fprintf('4. 正确的数据访问方法:\n\n');

fprintf('方法1: 直接使用矩阵索引（需要记住映射关系）\n');
fprintf('  %% 假设运行了第10~15炮\n');
fprintf('  shot_start = 10; shot_end = 15;\n');
fprintf('  data(1,:)     %% 第10炮数据\n');
fprintf('  data(3,:)     %% 第12炮数据\n');
fprintf('  data(6,:)     %% 第15炮数据\n\n');

fprintf('方法2: 使用计算索引（推荐）\n');
fprintf('  target_shot = 12;  %% 想要访问的炮号\n');
fprintf('  matrix_row = target_shot - shot_start + 1;  %% 计算矩阵行号\n');
fprintf('  shot_data = data(matrix_row, :);  %% 提取数据\n\n');

fprintf('方法3: 使用辅助函数（最安全）\n');
fprintf('  shot_data = get_shot_data(data, shot_start, maxt, target_shot);\n\n');

%% 5. 创建测试数据演示
fprintf('5. 创建测试数据进行演示:\n\n');

% 模拟参数
num_s = 67;          % 总炮数
N = 21;              % 检波器数
maxt = 1000;         % 时间采样点数

% 测试场景：运行第10~12炮
shot_start = 10;
shot_end = 12;
actual_num_shots = shot_end - shot_start + 1;

fprintf('测试参数:\n');
fprintf('  总炮数: %d\n', num_s);
fprintf('  运行范围: 第%d~%d炮\n', shot_start, shot_end);
fprintf('  实际运行炮数: %d\n', actual_num_shots);
fprintf('  检波器数: %d\n', N);
fprintf('  时间采样点数: %d\n', maxt);

% 创建模拟数据（每炮有不同的特征）
fprintf('\n创建模拟数据...\n');
data = zeros(actual_num_shots, N*maxt);

for i = 1:actual_num_shots
    actual_shot = shot_start + i - 1;
    % 为每炮创建不同的信号特征（用炮号作为幅度因子）
    for j = 1:N
        t = (1:maxt) / maxt;
        signal = actual_shot * sin(2*pi*5*t) .* exp(-t*2);  % 炮号影响幅度
        data(i, (j-1)*maxt+1:j*maxt) = signal;
    end
end

fprintf('模拟数据创建完成\n');

%% 6. 数据访问演示
fprintf('\n6. 数据访问演示:\n\n');

% 演示不同的访问方法
target_shots = [10, 11, 12];

for target_shot = target_shots
    fprintf('访问第%d炮数据:\n', target_shot);
    
    % 方法1：直接索引
    matrix_row = target_shot - shot_start + 1;
    fprintf('  方法1: data(%d,:) - 直接使用矩阵行号\n', matrix_row);
    
    % 方法2：计算索引
    calculated_row = target_shot - shot_start + 1;
    fprintf('  方法2: matrix_row = %d - %d + 1 = %d\n', target_shot, shot_start, calculated_row);
    
    % 提取第1道数据作为示例
    first_channel = data(matrix_row, 1:maxt);
    max_amplitude = max(abs(first_channel));
    fprintf('  第1道最大幅度: %.2f (应该约等于%d)\n', max_amplitude, target_shot);
    
    fprintf('\n');
end

%% 7. 可视化演示
fprintf('7. 可视化演示:\n\n');

% 显示所有炮的数据
figure('Name', '所有炮数据总览', 'Position', [100, 500, 800, 400]);
imagesc(data);
colorbar;
title(sprintf('第%d~%d炮数据总览', shot_start, shot_end));
xlabel('时间采样点 (所有道连续排列)');
ylabel('炮号 (矩阵行号)');

% 添加行号标签
yticks(1:actual_num_shots);
yticklabels(arrayfun(@(x) sprintf('第%d炮', x), shot_start:shot_end, 'UniformOutput', false));

% 显示单炮波形对比
figure('Name', '单炮波形对比', 'Position', [100, 50, 800, 400]);
colors = {'b-', 'r--', 'g:'};
legend_labels = {};

for i = 1:actual_num_shots
    actual_shot = shot_start + i - 1;
    first_channel = data(i, 1:maxt);
    
    plot(first_channel, colors{mod(i-1, 3)+1}, 'LineWidth', 2);
    hold on;
    
    legend_labels{end+1} = sprintf('第%d炮', actual_shot);
end

title('各炮第1道波形对比');
xlabel('时间采样点');
ylabel('幅度');
legend(legend_labels);
grid on;

%% 8. 保存演示数据
fprintf('\n8. 保存演示数据:\n\n');

% 保存数据（模拟FDTD程序的保存格式）
demo_filename = 'demo_shot_mapping_data.mat';
current_time = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));

save(demo_filename, ...
    'data', ...
    'shot_start', 'shot_end', 'actual_num_shots', ...
    'num_s', 'N', 'maxt', ...
    'current_time');

fprintf('演示数据已保存到: %s\n', demo_filename);

%% 9. 调用炮号映射辅助函数
fprintf('\n9. 调用炮号映射辅助函数:\n\n');

if exist('shot_mapping_helper.m', 'file')
    fprintf('调用 shot_mapping_helper 分析演示数据...\n\n');
    shot_mapping_helper(demo_filename);
else
    fprintf('炮号映射辅助函数不存在，请确保 shot_mapping_helper.m 在当前路径中\n');
end

%% 10. 总结和建议
fprintf('\n=== 总结和建议 ===\n');
fprintf('1. 程序设计是正确的：\n');
fprintf('   - 数据矩阵大小 = 实际运行炮数 × (检波器数 × 时间点数)\n');
fprintf('   - 保存了完整的炮号映射信息\n');
fprintf('   - 用户可以通过 shot_start, shot_end 参数了解对应关系\n\n');

fprintf('2. 推荐的使用方法：\n');
fprintf('   - 运行程序后，使用 shot_mapping_helper() 查看映射关系\n');
fprintf('   - 使用 get_shot_data() 函数安全地提取特定炮的数据\n');
fprintf('   - 在分析结果时，始终参考 shot_start 和 shot_end 参数\n\n');

fprintf('3. 避免混淆的建议：\n');
fprintf('   - 在变量名中包含实际炮号信息\n');
fprintf('   - 在图表标题中明确标注实际炮号\n');
fprintf('   - 使用注释记录数据的来源炮号范围\n\n');

fprintf('=== 演示完成 ===\n');

% 清理临时变量（保留重要的演示数据）
clear i j t signal matrix_row calculated_row first_channel max_amplitude;
clear colors legend_labels target_shot target_shots scenarios;
