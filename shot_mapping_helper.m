function shot_mapping_helper(data_filename)
%% 炮号映射辅助函数
% 功能：帮助用户理解数据矩阵与实际炮号的对应关系
% 输入：data_filename - 数据文件名（可选，如果不提供则查找最新文件）

if nargin < 1
    % 自动查找最新的数据文件
    files_2d = dir('FDTD_SeismicLogging_*.mat');
    files_3d = dir('FDTD_3D_Simple_*.mat');
    
    if isempty(files_2d) && isempty(files_3d)
        error('未找到FDTD数据文件，请先运行FDTD程序');
    end
    
    % 选择最新的文件
    all_files = [files_2d; files_3d];
    [~, idx] = max([all_files.datenum]);
    data_filename = all_files(idx).name;
    
    fprintf('自动选择最新数据文件: %s\n', data_filename);
end

% 检查文件是否存在
if ~exist(data_filename, 'file')
    error('数据文件不存在: %s', data_filename);
end

% 加载数据
fprintf('加载数据文件: %s\n', data_filename);
load(data_filename);

% 检查必要变量是否存在
required_vars = {'data', 'shot_start', 'shot_end', 'actual_num_shots', 'N', 'maxt'};
for i = 1:length(required_vars)
    if ~exist(required_vars{i}, 'var')
        error('数据文件中缺少必要变量: %s', required_vars{i});
    end
end

%% 显示炮号映射信息
fprintf('\n=== 炮号映射信息 ===\n');
fprintf('数据文件: %s\n', data_filename);
fprintf('总炮数: %d\n', num_s);
fprintf('运行炮数范围: 第%d炮 ~ 第%d炮\n', shot_start, shot_end);
fprintf('实际运行炮数: %d\n', actual_num_shots);
fprintf('检波器数量: %d\n', N);
fprintf('时间采样点数: %d\n', maxt);
fprintf('数据矩阵尺寸: %d × %d\n', size(data,1), size(data,2));

%% 创建炮号映射表
fprintf('\n=== 数据矩阵行号与实际炮号对应表 ===\n');
fprintf('数据矩阵行号 | 实际炮号 | 炮点位置\n');
fprintf('------------|----------|----------\n');

for i = 1:actual_num_shots
    actual_shot = shot_start + i - 1;
    if exist('nz', 'var') && exist('pml', 'var') && exist('len_RtoR', 'var')
        pos_s = nz - 3*pml - (actual_shot-1)*len_RtoR;
        fprintf('     %2d     |    %2d    |   %4d\n', i, actual_shot, pos_s);
    else
        fprintf('     %2d     |    %2d    |    --\n', i, actual_shot);
    end
end

%% 数据访问示例
fprintf('\n=== 数据访问示例 ===\n');

% 示例1：访问特定炮的数据
if actual_num_shots >= 1
    example_shot = shot_start;
    matrix_row = 1;
    fprintf('访问第%d炮数据:\n', example_shot);
    fprintf('  data(%d,:)  %% 第%d炮所有道的数据\n', matrix_row, example_shot);
    fprintf('  data(%d,1:%d)  %% 第%d炮第1道的数据\n', matrix_row, maxt, example_shot);
end

% 示例2：访问中间炮的数据
if actual_num_shots >= 2
    mid_index = ceil(actual_num_shots/2);
    mid_shot = shot_start + mid_index - 1;
    fprintf('\n访问第%d炮数据:\n', mid_shot);
    fprintf('  data(%d,:)  %% 第%d炮所有道的数据\n', mid_index, mid_shot);
end

% 示例3：访问最后一炮的数据
if actual_num_shots >= 1
    last_shot = shot_end;
    last_row = actual_num_shots;
    fprintf('\n访问第%d炮数据:\n', last_shot);
    fprintf('  data(%d,:)  %% 第%d炮所有道的数据\n', last_row, last_shot);
end

%% 可视化示例
fprintf('\n=== 可视化示例 ===\n');
fprintf('显示所有运行炮的数据:\n');
fprintf('  figure; imagesc(data); colorbar;\n');
fprintf('  title(''第%d~%d炮数据''); xlabel(''时间采样点''); ylabel(''炮号(矩阵行)'');\n', shot_start, shot_end);

fprintf('\n显示特定炮的波形:\n');
fprintf('  figure; plot(data(1,:)); title(''第%d炮数据'');\n', shot_start);
fprintf('  figure; plot(data(%d,:)); title(''第%d炮数据'');\n', actual_num_shots, shot_end);

fprintf('\n对比不同炮的波形:\n');
fprintf('  figure; \n');
fprintf('  plot(data(1,1:2000), ''b-'', ''LineWidth'', 2); hold on;\n');
if actual_num_shots > 1
    fprintf('  plot(data(%d,1:2000), ''r--'', ''LineWidth'', 2);\n', actual_num_shots);
    fprintf('  legend(''第%d炮'', ''第%d炮''); title(''波形对比'');\n', shot_start, shot_end);
else
    fprintf('  title(''第%d炮波形'');\n', shot_start);
end

%% 数据提取函数
fprintf('\n=== 数据提取函数 ===\n');
fprintf('提取特定炮号的数据:\n');
fprintf('  shot_data = get_shot_data(data, %d, %d, target_shot);\n', shot_start, maxt);
fprintf('  其中 target_shot 是要提取的实际炮号\n');

%% 创建数据提取函数
fprintf('\n正在创建数据提取辅助函数...\n');

extract_function_code = sprintf([...
    'function shot_data = get_shot_data(data, shot_start, maxt, target_shot)\n' ...
    '%% 根据实际炮号提取数据\n' ...
    '%% 输入:\n' ...
    '%%   data - 数据矩阵\n' ...
    '%%   shot_start - 起始炮号\n' ...
    '%%   maxt - 时间采样点数\n' ...
    '%%   target_shot - 目标炮号\n' ...
    '%% 输出:\n' ...
    '%%   shot_data - 目标炮的数据 (N道 × maxt时间点)\n\n' ...
    'if target_shot < shot_start\n' ...
    '    error(''目标炮号%%d小于起始炮号%%d'', target_shot, shot_start);\n' ...
    'end\n\n' ...
    'matrix_row = target_shot - shot_start + 1;\n' ...
    'if matrix_row > size(data, 1)\n' ...
    '    error(''目标炮号%%d超出数据范围'', target_shot);\n' ...
    'end\n\n' ...
    'shot_data_1d = data(matrix_row, :);\n' ...
    'N = length(shot_data_1d) / maxt;\n' ...
    'shot_data = reshape(shot_data_1d, maxt, N)'''';\n' ...
    'end\n'
]);

fid = fopen('get_shot_data.m', 'w');
fprintf(fid, '%s', extract_function_code);
fclose(fid);

fprintf('已创建数据提取函数: get_shot_data.m\n');

%% 验证数据完整性
fprintf('\n=== 数据完整性验证 ===\n');

expected_cols = N * maxt;
actual_cols = size(data, 2);

if actual_cols == expected_cols
    fprintf('✓ 数据矩阵列数正确: %d = %d道 × %d时间点\n', actual_cols, N, maxt);
else
    fprintf('✗ 数据矩阵列数异常: 期望%d，实际%d\n', expected_cols, actual_cols);
end

if size(data, 1) == actual_num_shots
    fprintf('✓ 数据矩阵行数正确: %d = 实际运行炮数\n', actual_num_shots);
else
    fprintf('✗ 数据矩阵行数异常: 期望%d，实际%d\n', actual_num_shots, size(data, 1));
end

% 检查数据范围
data_max = max(data(:));
data_min = min(data(:));
fprintf('数据值范围: [%.2e, %.2e]\n', data_min, data_max);

if data_max == 0 && data_min == 0
    fprintf('⚠️  警告: 所有数据为零，可能计算有问题\n');
elseif abs(data_max) > 1e10 || abs(data_min) > 1e10
    fprintf('⚠️  警告: 数据值过大，可能存在数值不稳定\n');
else
    fprintf('✓ 数据值范围正常\n');
end

fprintf('\n=== 炮号映射分析完成 ===\n');

end
