%% 三维与二维FDTD程序对比测试脚本
% 功能：验证三维程序的正确性，对比二维和三维结果

clc; clear; close all;

fprintf('=== 三维与二维FDTD程序对比测试 ===\n\n');

%% 1. 程序存在性检查
fprintf('1. 检查程序文件...\n');

files_to_check = {'kuaisu_main.m', 'kuaisu_main_3D_simple.m'};
for i = 1:length(files_to_check)
    if exist(files_to_check{i}, 'file')
        fprintf('  ✓ %s 存在\n', files_to_check{i});
    else
        fprintf('  ✗ %s 不存在\n', files_to_check{i});
        error('缺少必要的程序文件');
    end
end

%% 2. 内存需求估算
fprintf('\n2. 内存需求估算...\n');

% 二维程序内存需求
nx_2d = 300; nz_2d = 1300;  % 二维网格
fields_2d = 6;  % 2个速度场 + 3个应力场，每个4个分量（主场+3个PML）
memory_2d = fields_2d * 4 * nx_2d * nz_2d * 8 / 1024^3;

% 三维程序内存需求
nx_3d = 140; ny_3d = 140; nz_3d = 640;  % 三维网格（简化版）
fields_3d = 9;  % 3个速度场 + 6个应力场，每个1个分量（简化版）
memory_3d = fields_3d * nx_3d * ny_3d * nz_3d * 8 / 1024^3;

fprintf('  二维程序内存需求: %.2f GB\n', memory_2d);
fprintf('  三维程序内存需求: %.2f GB\n', memory_3d);
fprintf('  内存增长倍数: %.1f倍\n', memory_3d/memory_2d);

if memory_3d > 8
    fprintf('  ⚠️  警告：三维程序内存需求较大，建议在高内存机器上运行\n');
end

%% 3. 计算时间估算
fprintf('\n3. 计算时间估算...\n');

% 假设二维程序每炮需要10秒
time_per_shot_2d = 10;  % 秒
shots_2d = 3;  % 测试炮数

% 三维程序时间估算（基于网格点数比例）
grid_ratio = (nx_3d * ny_3d * nz_3d) / (nx_2d * nz_2d);
time_per_shot_3d = time_per_shot_2d * grid_ratio * 1.5;  % 额外1.5倍复杂度
shots_3d = 3;

fprintf('  二维程序预计时间: %.1f分钟 (%d炮)\n', time_per_shot_2d*shots_2d/60, shots_2d);
fprintf('  三维程序预计时间: %.1f分钟 (%d炮)\n', time_per_shot_3d*shots_3d/60, shots_3d);
fprintf('  时间增长倍数: %.1f倍\n', time_per_shot_3d/time_per_shot_2d);

%% 4. 参数对比
fprintf('\n4. 关键参数对比...\n');

% 模拟程序中的关键参数
params = struct();
params.f0 = 10000;      % 主频率
params.vp1 = 1500;      % 井孔纵波速度
params.vp2 = 4500;      % 地层纵波速度
params.cal = 0.1;       % 井径
params.dx = 0.015;      % 空间步长

fprintf('  震源主频: %d Hz\n', params.f0);
fprintf('  井孔纵波速度: %d m/s\n', params.vp1);
fprintf('  地层纵波速度: %d m/s\n', params.vp2);
fprintf('  井径: %.1f m\n', params.cal);
fprintf('  空间步长: %.3f m\n', params.dx);

%% 5. 运行建议
fprintf('\n5. 运行建议...\n');

fprintf('  二维程序测试:\n');
fprintf('    - 修改 kuaisu_main.m 中 shot_start=1, shot_end=3\n');
fprintf('    - 运行: run(''kuaisu_main.m'')\n');
fprintf('    - 预计时间: %.1f分钟\n', time_per_shot_2d*3/60);

fprintf('\n  三维程序测试:\n');
fprintf('    - 运行: run(''kuaisu_main_3D_simple.m'')\n');
fprintf('    - 预计时间: %.1f分钟\n', time_per_shot_3d*3/60);
fprintf('    - 注意: 确保有足够内存 (%.1f GB)\n', memory_3d);

%% 6. 结果对比方法
fprintf('\n6. 结果对比方法...\n');

comparison_code = sprintf([...
    '%% 结果对比代码示例\n' ...
    '%% 1. 加载二维结果\n' ...
    'load(''FDTD_SeismicLogging_*.mat'');  %% 二维结果\n' ...
    'data_2d = data;\n\n' ...
    '%% 2. 加载三维结果\n' ...
    'load(''FDTD_3D_Simple_*.mat'');      %% 三维结果\n' ...
    'data_3d = data;\n\n' ...
    '%% 3. 对比第1炮数据\n' ...
    'figure;\n' ...
    'subplot(2,1,1); plot(data_2d(1,:)); title(''二维第1炮'');\n' ...
    'subplot(2,1,2); plot(data_3d(1,:)); title(''三维第1炮'');\n\n' ...
    '%% 4. 对比波形特征\n' ...
    'figure;\n' ...
    'plot(data_2d(1,1:2000), ''b-'', ''LineWidth'', 2); hold on;\n' ...
    'plot(data_3d(1,1:2000), ''r--'', ''LineWidth'', 2);\n' ...
    'legend(''二维'', ''三维''); title(''波形对比'');\n' ...
    'xlabel(''时间采样点''); ylabel(''幅度'');\n'
]);

fprintf('%s\n', comparison_code);

%% 7. 预期结果分析
fprintf('\n7. 预期结果分析...\n');

fprintf('  相似性预期:\n');
fprintf('    - 主要波形特征应该相似\n');
fprintf('    - 到达时间应该基本一致\n');
fprintf('    - 幅度可能有差异（三维几何扩散效应）\n');

fprintf('\n  差异原因:\n');
fprintf('    - 三维几何扩散 vs 二维柱面扩散\n');
fprintf('    - 边界条件处理差异\n');
fprintf('    - 数值精度差异\n');

%% 8. 故障排除
fprintf('\n8. 故障排除...\n');

fprintf('  常见问题:\n');
fprintf('    - 内存不足: 减小网格尺寸或增加系统内存\n');
fprintf('    - 计算时间过长: 减少炮数或时间步数\n');
fprintf('    - 结果异常: 检查参数设置和边界条件\n');

fprintf('\n  调试建议:\n');
fprintf('    - 先运行单炮测试\n');
fprintf('    - 检查中间结果的合理性\n');
fprintf('    - 对比关键参数的数值范围\n');

%% 9. 性能优化建议
fprintf('\n9. 性能优化建议...\n');

fprintf('  三维程序优化:\n');
fprintf('    - 使用向量化操作替代循环\n');
fprintf('    - 考虑并行计算（parfor）\n');
fprintf('    - 优化内存访问模式\n');
fprintf('    - 使用单精度浮点数（如果精度允许）\n');

%% 10. 总结
fprintf('\n=== 测试准备完成 ===\n');
fprintf('现在可以分别运行二维和三维程序进行对比测试\n');
fprintf('建议先运行二维程序验证基础功能，再运行三维程序\n');
fprintf('运行完成后使用上述对比代码分析结果差异\n');

% 创建快速测试脚本
fprintf('\n正在创建快速测试脚本...\n');

% 二维快速测试
quick_2d_code = sprintf([...
    '%% 二维程序快速测试\n' ...
    'fprintf(''开始二维程序测试...\\n'');\n' ...
    'tic;\n' ...
    'run(''kuaisu_main.m'');\n' ...
    'time_2d = toc;\n' ...
    'fprintf(''二维程序完成，用时: %%.2f 秒\\n'', time_2d);\n'
]);

fid = fopen('quick_test_2d.m', 'w');
fprintf(fid, '%s', quick_2d_code);
fclose(fid);

% 三维快速测试
quick_3d_code = sprintf([...
    '%% 三维程序快速测试\n' ...
    'fprintf(''开始三维程序测试...\\n'');\n' ...
    'tic;\n' ...
    'run(''kuaisu_main_3D_simple.m'');\n' ...
    'time_3d = toc;\n' ...
    'fprintf(''三维程序完成，用时: %%.2f 秒\\n'', time_3d);\n'
]);

fid = fopen('quick_test_3d.m', 'w');
fprintf(fid, '%s', quick_3d_code);
fclose(fid);

fprintf('已创建快速测试脚本:\n');
fprintf('  - quick_test_2d.m (二维程序测试)\n');
fprintf('  - quick_test_3d.m (三维程序测试)\n');

fprintf('\n=== 对比测试脚本准备完成 ===\n');
