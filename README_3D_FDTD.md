# 三维声波测井FDTD正演模拟程序

## 概述

本程序是基于二维声波测井FDTD程序扩展的三维版本，实现了三维弹性波传播的时域有限差分算法，适用于声波测井正演模拟。

## 程序文件说明

### 主程序文件
- **`kuaisu_main.m`** - 原始二维FDTD程序
- **`kuaisu_main_3D_simple.m`** - 简化版三维FDTD程序（推荐使用）
- **`kuaisu_main_3D.m`** - 完整版三维FDTD程序（包含复杂PML边界，开发中）

### 测试和工具文件
- **`test_3D_vs_2D.m`** - 二维与三维程序对比测试脚本
- **`quick_test_2d.m`** - 二维程序快速测试（自动生成）
- **`quick_test_3d.m`** - 三维程序快速测试（自动生成）

## 主要特点

### 三维扩展特性
1. **三维网格**: 从二维 `nx×nz` 扩展到三维 `nx×ny×nz`，其中 `ny=nx`
2. **三维场变量**: 
   - 速度场：`Vx, Vy, Vz` (3个分量)
   - 应力场：`Txx, Tyy, Tzz, Txy, Txz, Tyz` (6个分量)
3. **三维地质模型**: 圆柱形井孔 + 环形侵入带 + 围岩
4. **三维FDTD算法**: 完整的三维弹性波方程数值求解

### 简化设计
- 去除复杂的三维PML边界条件（简化版）
- 优化内存使用和计算效率
- 保持与二维程序相同的物理参数和测井几何配置

## 系统要求

### 硬件要求
- **内存**: 建议 8GB 以上（三维计算内存需求较大）
- **CPU**: 多核处理器（计算密集型）
- **存储**: 至少 1GB 可用空间（用于结果存储）

### 软件要求
- MATLAB R2018b 或更高版本
- 无需额外工具箱

## 使用方法

### 1. 快速开始

```matlab
% 运行三维程序（默认前3炮）
run('kuaisu_main_3D_simple.m')
```

### 2. 参数设置

在 `kuaisu_main_3D_simple.m` 中可以修改以下关键参数：

```matlab
% 炮数控制
shot_start = 1;      % 起始炮号
shot_end = 3;        % 结束炮号（建议不超过5炮）

% 网格尺寸（影响内存和计算时间）
pml = 20;            % 边界层厚度
nx = 2*pml+100;      % X方向网格数
ny = nx;             % Y方向网格数
nz = 2*pml+600;      % Z方向网格数
```

### 3. 对比测试

```matlab
% 运行对比测试脚本
run('test_3D_vs_2D.m')

% 分别测试二维和三维程序
run('quick_test_2d.m')  % 二维程序测试
run('quick_test_3d.m')  % 三维程序测试
```

## 结果分析

### 数据文件
程序运行完成后会生成 `.mat` 文件，包含：
- `data`: 主要结果数据矩阵
- `vp, vs, dens`: 三维地层参数
- `X`: 最后一炮的详细数据
- 其他计算参数和设置

### 炮号映射机制

**重要说明**: 程序支持运行任意炮数范围，但数据矩阵总是从第1行开始存储。

```matlab
% 示例：运行第10~15炮
shot_start = 10; shot_end = 15;  % 在程序中设置

% 运行后，data矩阵尺寸为 6×(N*maxt)
% data(1,:) 对应第10炮数据
% data(6,:) 对应第15炮数据
```

**正确的数据访问方法**:
```matlab
% 方法1：使用炮号映射辅助函数（推荐）
shot_mapping_helper();  % 查看映射关系
target_shot = 12;       % 想要访问的实际炮号
shot_data = get_shot_data(data, shot_start, maxt, target_shot);

% 方法2：手动计算矩阵行号
target_shot = 12;
matrix_row = target_shot - shot_start + 1;  % 计算得到第3行
shot_data_1d = data(matrix_row, :);
```

### 可视化示例

```matlab
% 加载结果
load('FDTD_3D_Simple_*.mat');

% 显示测井数据（注意标注实际炮号）
figure; imagesc(data);
title(sprintf('三维声波测井数据 (第%d~%d炮)', shot_start, shot_end));
xlabel('时间采样点'); ylabel('炮号(矩阵行)');

% 显示特定炮的数据
figure; plot(data(1,:));
title(sprintf('第%d炮数据', shot_start));

% 显示地层模型
figure; imagesc(squeeze(vp(:,:,med_x)));
title('XZ剖面纵波速度');

figure; imagesc(squeeze(vp(nz/2,:,:)));
title('XY剖面纵波速度');
```

## 性能优化

### 内存优化
1. 减小网格尺寸：`nx, ny, nz`
2. 减少运行炮数：`shot_end`
3. 使用单精度：将 `zeros()` 改为 `zeros(..., 'single')`

### 计算优化
1. 减少时间步数：调整震源持续时间
2. 使用并行计算：在循环中使用 `parfor`
3. 向量化操作：避免嵌套循环

## 炮号映射详解

### 问题背景
用户发现程序可以选择任意炮数范围运行（如第10~15炮），但保存的数据矩阵总是从第1行开始，容易造成混淆。

### 映射机制
程序采用**相对索引**存储方式：
- `data` 矩阵大小：`[actual_num_shots × (N*maxt)]`
- `data(1,:)` 对应 `shot_start` 炮的数据
- `data(actual_num_shots,:)` 对应 `shot_end` 炮的数据

### 映射公式
```matlab
% 从实际炮号计算矩阵行号
matrix_row = target_shot - shot_start + 1;

% 从矩阵行号计算实际炮号
actual_shot = matrix_row + shot_start - 1;
```

### 辅助工具
```matlab
% 查看详细映射关系
shot_mapping_helper();

% 安全提取特定炮数据
shot_data = get_shot_data(data, shot_start, maxt, target_shot);

% 演示映射机制
run('demo_shot_mapping.m');
```

### 映射示例
| 运行设置 | 数据矩阵 | 映射关系 |
|---------|---------|---------|
| shot_start=1, shot_end=3 | 3×(N*maxt) | data(1,:)→第1炮, data(3,:)→第3炮 |
| shot_start=10, shot_end=12 | 3×(N*maxt) | data(1,:)→第10炮, data(3,:)→第12炮 |
| shot_start=25, shot_end=25 | 1×(N*maxt) | data(1,:)→第25炮 |

## 常见问题

### Q1: 内存不足错误
**解决方案**:
- 减小网格尺寸（如 `nx=80, ny=80, nz=400`）
- 关闭其他程序释放内存
- 使用单精度浮点数

### Q2: 计算时间过长
**解决方案**:
- 减少炮数（如只运行1炮测试）
- 减小网格尺寸
- 使用更快的计算机

### Q3: 结果与二维程序差异很大
**解决方案**:
- 检查参数设置是否一致
- 确认地层模型构建正确
- 对比关键物理量的数值范围

### Q4: 数据矩阵与炮号对应关系混淆
**解决方案**:
- 使用 `shot_mapping_helper()` 查看详细映射关系
- 使用 `get_shot_data()` 函数安全提取数据
- 记住公式：`matrix_row = target_shot - shot_start + 1`
- 在分析时始终参考保存的 `shot_start` 和 `shot_end` 参数

## 技术细节

### 三维FDTD方程
程序实现了完整的三维弹性波方程：

**应力更新**:
- `Txx = (λ+2μ)∂Vx/∂x + λ∂Vy/∂y + λ∂Vz/∂z`
- `Tyy = λ∂Vx/∂x + (λ+2μ)∂Vy/∂y + λ∂Vz/∂z`
- `Tzz = λ∂Vx/∂x + λ∂Vy/∂y + (λ+2μ)∂Vz/∂z`
- `Txy = μ(∂Vx/∂y + ∂Vy/∂x)`
- `Txz = μ(∂Vx/∂z + ∂Vz/∂x)`
- `Tyz = μ(∂Vy/∂z + ∂Vz/∂y)`

**速度更新**:
- `ρ∂Vx/∂t = ∂Txx/∂x + ∂Txy/∂y + ∂Txz/∂z`
- `ρ∂Vy/∂t = ∂Txy/∂x + ∂Tyy/∂y + ∂Tyz/∂z`
- `ρ∂Vz/∂t = ∂Txz/∂x + ∂Tyz/∂y + ∂Tzz/∂z`

### 地层模型
- **井孔**: 圆柱形，半径由 `cal` 参数控制
- **侵入带**: 环形区域，位于井孔周围
- **围岩**: 均匀介质，填充剩余空间

## 开发计划

### 已完成
- [x] 三维网格和场变量设计
- [x] 三维地质模型构建
- [x] 三维FDTD更新方程实现
- [x] 数据采集和存储
- [x] 基本测试和验证

### 待完成
- [ ] 完整三维PML边界条件
- [ ] 并行计算优化
- [ ] GPU加速版本
- [ ] 更多地层模型选项

## 联系信息

如有问题或建议，请通过以下方式联系：
- 检查代码注释获取详细说明
- 运行测试脚本验证程序功能
- 参考二维程序的相关文档

---

**注意**: 三维FDTD计算资源需求较大，建议先在小规模参数下测试，确认程序正常运行后再进行大规模计算。
