clc; clear;

%% 从主程序中提取关键参数
% 物理参数
f0=10*10^3;      % 主频率 [Hz]
vp1=1500;        % 井孔纵波速度 [m/s]
cal=0.1;         % 井径 [m]

% 网格参数
la1=vp1/f0;      % 波长计算 [m]
dx=la1/10;       % X方向空间步长 [m]
dz=la1/10;       % Z方向空间步长 [m]

% 仪器参数
num_s=67;        % 模拟炮数
L_StoR=1.5;      % 源距 [m]
L_RtoR=0.15;     % 接收器间距 [m]
len_StoR=fix(L_StoR/dz);  % 源距网格点数
len_RtoR=fix(L_RtoR/dz);  % 接收器间距网格点数

% 计算域参数
pml=50;          % PML吸收边界层厚度
nx=2*pml+200;    % X方向总网格数
nz=2*pml+1200;   % Z方向总网格数

% 井孔位置
med_x=fix(nx/2)-fix(nx/4);  % 井轴在X方向的位置
l_cal=ceil(cal/dx)/1;        % 井径对应的网格点数

% 侵入带参数
Formation_D=1.0;             % 侵入带径向厚度 [m]
Formation_DIter=ceil(Formation_D/dz);  % 侵入带径向网格数
Formation_H=1.0;             % 侵入带轴向高度 [m]
Formation_HIter=ceil(Formation_H/dz);  % 侵入带轴向网格数

%% 计算侵入带位置
% 侵入带在Z方向的范围（网格坐标）
invasion_z_start = nz/2 - ceil(Formation_HIter/2);
invasion_z_end = nz/2 + ceil(Formation_HIter/2);
invasion_z_center = nz/2;

% 侵入带在X方向的范围（网格坐标）
invasion_x_start = med_x + l_cal + 1;
invasion_x_end = med_x + l_cal + Formation_DIter;

% 转换为物理坐标
invasion_z_start_phys = invasion_z_start * dz;
invasion_z_end_phys = invasion_z_end * dz;
invasion_z_center_phys = invasion_z_center * dz;
invasion_x_start_phys = invasion_x_start * dx;
invasion_x_end_phys = invasion_x_end * dx;

%% 计算炮点位置
shot_positions = zeros(num_s, 1);  % 存储所有炮点的Z坐标（网格）
shot_positions_phys = zeros(num_s, 1);  % 存储所有炮点的Z坐标（物理）

for count_s = 1:num_s
    pos_s = nz - 3*pml - (count_s-1)*len_RtoR;  % 炮点Z轴位置（网格坐标）
    shot_positions(count_s) = pos_s;
    shot_positions_phys(count_s) = pos_s * dz;
end

%% 分析哪些炮点与侵入带相关
% 找出炮点在侵入带范围内的炮号
shots_in_invasion = find(shot_positions >= invasion_z_start & shot_positions <= invasion_z_end);

% 找出炮点在侵入带附近的炮号（考虑波的传播范围）
wave_influence_range = 2.0;  % 波影响范围 [m]
wave_influence_grid = ceil(wave_influence_range / dz);

shots_near_invasion = find(shot_positions >= (invasion_z_start - wave_influence_grid) & ...
                          shot_positions <= (invasion_z_end + wave_influence_grid));

%% 显示结果
fprintf('=== 侵入带位置分析 ===\n');
fprintf('网格参数：dx = %.4f m, dz = %.4f m\n', dx, dz);
fprintf('计算域：nx = %d, nz = %d\n', nx, nz);
fprintf('井孔中心X坐标：%d (网格), %.3f m (物理)\n', med_x, med_x*dx);
fprintf('\n');

fprintf('=== 侵入带几何参数 ===\n');
fprintf('径向厚度：%.2f m (%d 网格点)\n', Formation_D, Formation_DIter);
fprintf('轴向高度：%.2f m (%d 网格点)\n', Formation_H, Formation_HIter);
fprintf('\n');

fprintf('=== 侵入带位置（网格坐标）===\n');
fprintf('Z方向范围：%d ~ %d (中心：%d)\n', invasion_z_start, invasion_z_end, invasion_z_center);
fprintf('X方向范围：%d ~ %d\n', invasion_x_start, invasion_x_end);
fprintf('\n');

fprintf('=== 侵入带位置（物理坐标）===\n');
fprintf('Z方向范围：%.3f ~ %.3f m (中心：%.3f m)\n', ...
        invasion_z_start_phys, invasion_z_end_phys, invasion_z_center_phys);
fprintf('X方向范围：%.3f ~ %.3f m\n', invasion_x_start_phys, invasion_x_end_phys);
fprintf('\n');

fprintf('=== 炮点分析 ===\n');
fprintf('总炮数：%d\n', num_s);
fprintf('炮点间距：%.3f m (%d 网格点)\n', L_RtoR, len_RtoR);
fprintf('第1炮位置：%d (网格), %.3f m (物理)\n', shot_positions(1), shot_positions_phys(1));
fprintf('第%d炮位置：%d (网格), %.3f m (物理)\n', num_s, shot_positions(end), shot_positions_phys(end));
fprintf('\n');

fprintf('=== 侵入带相关炮点 ===\n');
if ~isempty(shots_in_invasion)
    fprintf('炮点在侵入带内：第 %d ~ %d 炮\n', min(shots_in_invasion), max(shots_in_invasion));
    fprintf('具体炮号：');
    fprintf('%d ', shots_in_invasion);
    fprintf('\n');
else
    fprintf('没有炮点位于侵入带内\n');
end

fprintf('\n炮点在侵入带影响范围内（±%.1f m）：第 %d ~ %d 炮\n', ...
        wave_influence_range, min(shots_near_invasion), max(shots_near_invasion));
fprintf('具体炮号：');
fprintf('%d ', shots_near_invasion);
fprintf('\n');

%% 可视化模型
figure('Position', [100, 100, 1000, 600]);

% 创建简化的模型示意图
z_model = (0:nz-1) * dz;
x_model = (0:nx-1) * dx;

% 绘制模型区域
subplot(1,2,1);
hold on;

% 绘制计算域边界
rectangle('Position', [0, 0, max(x_model), max(z_model)], ...
          'EdgeColor', 'k', 'LineWidth', 1, 'LineStyle', '--');

% 绘制PML边界
pml_thickness = pml * dx;
rectangle('Position', [pml_thickness, pml*dz, max(x_model)-2*pml_thickness, max(z_model)-2*pml*dz], ...
          'EdgeColor', 'b', 'LineWidth', 2, 'FaceColor', 'none');

% 绘制井孔
borehole_x = med_x * dx;
borehole_width = 2 * l_cal * dx;
rectangle('Position', [borehole_x-borehole_width/2, 0, borehole_width, max(z_model)], ...
          'EdgeColor', 'c', 'FaceColor', 'c', 'FaceAlpha', 0.3);

% 绘制侵入带
rectangle('Position', [invasion_x_start_phys, invasion_z_start_phys, ...
                      (invasion_x_end - invasion_x_start)*dx, ...
                      (invasion_z_end - invasion_z_start)*dz], ...
          'EdgeColor', 'r', 'FaceColor', 'r', 'FaceAlpha', 0.5);

% 绘制炮点
plot(borehole_x * ones(size(shot_positions_phys)), shot_positions_phys, 'ko', 'MarkerSize', 4);

% 标注侵入带相关炮点
if ~isempty(shots_in_invasion)
    plot(borehole_x * ones(size(shots_in_invasion)), shot_positions_phys(shots_in_invasion), ...
         'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
end

xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');
title('声波测井模型示意图');
legend('计算域', 'PML边界', '井孔', '侵入带', '炮点', '侵入带内炮点', 'Location', 'best');
grid on;
axis equal;
xlim([0, max(x_model)]);
ylim([0, max(z_model)]);

% 绘制炮点-侵入带关系图
subplot(1,2,2);
plot(1:num_s, shot_positions_phys, 'b-o', 'MarkerSize', 4);
hold on;
yline(invasion_z_start_phys, 'r--', '侵入带下边界', 'LineWidth', 2);
yline(invasion_z_end_phys, 'r--', '侵入带上边界', 'LineWidth', 2);
yline(invasion_z_center_phys, 'r-', '侵入带中心', 'LineWidth', 2);

if ~isempty(shots_in_invasion)
    plot(shots_in_invasion, shot_positions_phys(shots_in_invasion), ...
         'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
end

xlabel('炮号');
ylabel('炮点Z坐标 (m)');
title('炮点位置与侵入带关系');
grid on;
legend('炮点位置', '侵入带边界', '', '侵入带中心', '侵入带内炮点', 'Location', 'best');
